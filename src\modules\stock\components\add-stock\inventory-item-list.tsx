import React from "react";
import { UseFieldArrayReturn, UseFormReturn } from "react-hook-form";
import { FaBoxesPacking } from "react-icons/fa6";
import { Plus, Sparkles } from "lucide-react";
import { motion } from "framer-motion";
import { ICreateStock } from "../../validators/create-stock.validator";
import { InventoryItem } from "./inventory-item";

interface InventoryItemsListProps {
	methodsForm: UseFormReturn<ICreateStock>;
	inventoryFieldArray: UseFieldArrayReturn<ICreateStock, "inventories", "id">;
}

export const InventoryItemsList: React.FC<InventoryItemsListProps> = ({ methodsForm, inventoryFieldArray }) => {
	return (
		<div className="bg-gradient-to-br from-white to-gray-50/30 border-2 border-dashed rounded-xl border-gray-200 p-6 mb-6 shadow-sm">
			<div className="flex items-center justify-between mb-6">
				<div className="flex items-center gap-3">
					<div className="p-2 bg-mainColor/10 rounded-lg">
						<FaBoxesPacking className="text-mainColor text-lg" />
					</div>
					<div>
						<h3 className="text-lg font-semibold text-gray-700">Itens do Estoque</h3>
						<p className="text-sm text-gray-500">
							{inventoryFieldArray.fields.length === 0
								? "Nenhum item adicionado ainda"
								: `${inventoryFieldArray.fields.length} ${inventoryFieldArray.fields.length === 1 ? "item" : "itens"} adicionado${inventoryFieldArray.fields.length === 1 ? "" : "s"}`}
						</p>
					</div>
				</div>

				{inventoryFieldArray.fields.length > 0 && (
					<div className="flex items-center gap-2 text-sm text-gray-500">
						<Sparkles size={16} className="text-mainColor" />
						<span>Configure cada item abaixo</span>
					</div>
				)}
			</div>

			{inventoryFieldArray.fields.length === 0 ? (
				<div className="text-center py-12">
					<div className="p-4 bg-gray-100 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
						<FaBoxesPacking className="text-gray-400 text-2xl" />
					</div>
					<h4 className="text-lg font-medium text-gray-600 mb-2">Nenhum item adicionado</h4>
					<p className="text-gray-500 mb-6">Comece adicionando o primeiro item ao seu estoque</p>
				</div>
			) : (
				<div className="space-y-4 mb-6">
					{inventoryFieldArray.fields.map((field, index) => (
						<InventoryItem
							key={field.id}
							index={index}
							removeItem={() => inventoryFieldArray.remove(index)}
							isExistingIdProduct={Boolean(field?.stockMovement?.product?.id)}
							isExistingIdPackage={Boolean(field?.stockMovement?.product?.package?.id)}
							methodsForm={methodsForm}
						/>
					))}
				</div>
			)}

			<div className="flex justify-center">
				<motion.button
					type="button"
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
					onClick={() => {
						inventoryFieldArray.append({
							stockMovement: {
								description: "",
								product: {
									id: undefined,
									code: "",
									name: "",
									description: "",
									price: 0,
									costPrice: 0,
									barcode: "",
									ncm: "",
									categoryId: undefined,
									package: {
										id: undefined,
										code: "",
										name: "",
										barcode: "",
										quantityPerPackage: 0,
									},
								},
								quantity: 0,
							},
							expirationDate: "",
						});
					}}
					className="flex items-center gap-3 bg-gradient-to-r from-mainColor to-mainColor/90 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-200 hover:shadow-lg hover:shadow-mainColor/25 group"
				>
					<div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors">
						<Plus size={20} />
					</div>
					<span>Adicionar Novo Item</span>
					<Sparkles size={16} className="opacity-70 group-hover:opacity-100 transition-opacity" />
				</motion.button>
			</div>
		</div>
	);
};
